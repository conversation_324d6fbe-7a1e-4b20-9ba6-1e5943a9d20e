import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'user_model.dart';
import 'trip_model.dart';

class BookingModel {
  final String id;
  final String tripId;
  final String passengerId;
  final String driverId;
  final TripModel? trip;
  final UserModel? passenger;
  final UserModel? driver;
  final int seatsBooked;
  final double totalPrice;
  final String bookingType; // 'instant', 'manual'
  final String status; // 'pending', 'accepted', 'rejected', 'completed', 'cancelled'
  final String? message;
  final String? rejectionReason;
  final String? specialRequests;
  final Map<String, dynamic>? passengerDetails;
  final bool isPaid;
  final String? paymentMethod;
  final String? paymentReference;
  final DateTime? confirmedAt;
  final DateTime? cancelledAt;
  final DateTime? completedAt;
  final DateTime createdAt;
  final DateTime updatedAt;

  BookingModel({
    required this.id,
    required this.tripId,
    required this.passengerId,
    required this.driverId,
    this.trip,
    this.passenger,
    this.driver,
    required this.seatsBooked,
    required this.totalPrice,
    this.bookingType = 'manual',
    this.status = 'pending',
    this.message,
    this.rejectionReason,
    this.specialRequests,
    this.passengerDetails,
    this.isPaid = false,
    this.paymentMethod,
    this.paymentReference,
    this.confirmedAt,
    this.cancelledAt,
    this.completedAt,
    required this.createdAt,
    required this.updatedAt,
  });

  factory BookingModel.fromJson(Map<String, dynamic> json) {
    return BookingModel(
      id: _parseRequiredString(json['id'], 'id'),
      tripId: _parseRequiredString(json['trip_id'], 'trip_id'),
      passengerId: _parseRequiredString(json['passenger_id'], 'passenger_id'),
      driverId: _parseRequiredString(json['driver_id'], 'driver_id'),
      trip: json['trip'] != null
          ? TripModel.fromJson(json['trip'] as Map<String, dynamic>)
          : null,
      passenger: json['passenger'] != null
          ? UserModel.fromJson(json['passenger'] as Map<String, dynamic>)
          : null,
      driver: json['driver'] != null
          ? UserModel.fromJson(json['driver'] as Map<String, dynamic>)
          : null,
      seatsBooked: json['seats_booked'] as int,
      totalPrice: (json['total_price'] as num).toDouble(),
      bookingType: _parseNullableString(json['booking_type']) ?? 'manual',
      status: _parseNullableString(json['status']) ?? 'pending',
      message: _parseNullableString(json['message']),
      rejectionReason: _parseNullableString(json['rejection_reason']),
      specialRequests: _parseNullableString(json['special_requests']),
      passengerDetails: _parsePassengerDetails(json['passenger_details']),
      isPaid: json['is_paid'] as bool? ?? false,
      paymentMethod: _parseNullableString(json['payment_method']),
      paymentReference: _parseNullableString(json['payment_reference']),
      confirmedAt: _parseNullableDateTime(json['confirmed_at']),
      cancelledAt: _parseNullableDateTime(json['cancelled_at']),
      completedAt: _parseNullableDateTime(json['completed_at']),
      createdAt: _parseRequiredDateTime(json['created_at'], 'created_at'),
      updatedAt: _parseNullableDateTime(json['updated_at']) ?? DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'trip_id': tripId,
      'passenger_id': passengerId,
      'driver_id': driverId,
      'seats_booked': seatsBooked,
      'total_price': totalPrice,
      'booking_type': bookingType,
      'status': status,
      'message': message,
      'rejection_reason': rejectionReason,
      'special_requests': specialRequests,
      'passenger_details': passengerDetails,
      'is_paid': isPaid,
      'payment_method': paymentMethod,
      'payment_reference': paymentReference,
      'confirmed_at': confirmedAt?.toIso8601String(),
      'cancelled_at': cancelledAt?.toIso8601String(),
      'completed_at': completedAt?.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  BookingModel copyWith({
    String? id,
    String? tripId,
    String? passengerId,
    String? driverId,
    TripModel? trip,
    UserModel? passenger,
    UserModel? driver,
    int? seatsBooked,
    double? totalPrice,
    String? bookingType,
    String? status,
    String? message,
    String? rejectionReason,
    String? specialRequests,
    Map<String, dynamic>? passengerDetails,
    bool? isPaid,
    String? paymentMethod,
    String? paymentReference,
    DateTime? confirmedAt,
    DateTime? cancelledAt,
    DateTime? completedAt,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return BookingModel(
      id: id ?? this.id,
      tripId: tripId ?? this.tripId,
      passengerId: passengerId ?? this.passengerId,
      driverId: driverId ?? this.driverId,
      trip: trip ?? this.trip,
      passenger: passenger ?? this.passenger,
      driver: driver ?? this.driver,
      seatsBooked: seatsBooked ?? this.seatsBooked,
      totalPrice: totalPrice ?? this.totalPrice,
      bookingType: bookingType ?? this.bookingType,
      status: status ?? this.status,
      message: message ?? this.message,
      rejectionReason: rejectionReason ?? this.rejectionReason,
      specialRequests: specialRequests ?? this.specialRequests,
      passengerDetails: passengerDetails ?? this.passengerDetails,
      isPaid: isPaid ?? this.isPaid,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      paymentReference: paymentReference ?? this.paymentReference,
      confirmedAt: confirmedAt ?? this.confirmedAt,
      cancelledAt: cancelledAt ?? this.cancelledAt,
      completedAt: completedAt ?? this.completedAt,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // Status getters
  bool get isPending => status == 'pending';
  bool get isAccepted => status == 'accepted';
  bool get isRejected => status == 'rejected';
  bool get isCancelled => status == 'cancelled';
  bool get isCompleted => status == 'completed';

  // Legacy support
  bool get isConfirmed => status == 'accepted';

  String get statusText {
    switch (status) {
      case 'pending':
        return 'في الانتظار';
      case 'accepted':
        return 'مقبول';
      case 'rejected':
        return 'مرفوض';
      case 'cancelled':
        return 'ملغي';
      case 'completed':
        return 'مكتمل';
      default:
        return status;
    }
  }

  /// Get status color for UI
  Color get statusColor {
    switch (status) {
      case 'pending':
        return const Color(0xFFFF9800); // Orange
      case 'accepted':
        return const Color(0xFF4CAF50); // Green
      case 'rejected':
        return const Color(0xFFF44336); // Red
      case 'cancelled':
        return const Color(0xFF9E9E9E); // Grey
      case 'completed':
        return const Color(0xFF2196F3); // Blue
      default:
        return const Color(0xFF9E9E9E); // Grey
    }
  }

  /// Get status icon for UI
  IconData get statusIcon {
    switch (status) {
      case 'pending':
        return Icons.schedule;
      case 'accepted':
        return Icons.check_circle;
      case 'rejected':
        return Icons.cancel;
      case 'cancelled':
        return Icons.block;
      case 'completed':
        return Icons.done_all;
      default:
        return Icons.help;
    }
  }

  /// Check if booking can be cancelled by passenger
  bool get canBeCancelled => isPending || isAccepted;

  /// Check if booking can be accepted/rejected by driver
  bool get canBeProcessed => isPending;

  /// Get formatted price per seat
  String get pricePerSeat => (totalPrice / seatsBooked).toStringAsFixed(0);

  /// Get formatted total price
  String get formattedTotalPrice => '${totalPrice.toStringAsFixed(0)} درهم';

  /// Get formatted seats text
  String get seatsText {
    if (seatsBooked == 1) {
      return 'مقعد واحد';
    } else if (seatsBooked == 2) {
      return 'مقعدان';
    } else {
      return '$seatsBooked مقاعد';
    }
  }

  /// Get booking type text in Arabic
  String get bookingTypeText {
    switch (bookingType) {
      case 'instant':
        return 'حجز فوري';
      case 'manual':
        return 'حجز بالموافقة';
      default:
        return bookingType;
    }
  }

  /// Get time since booking was created
  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(createdAt);

    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inMinutes < 60) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inHours < 24) {
      return 'منذ ${difference.inHours} ساعة';
    } else {
      return 'منذ ${difference.inDays} يوم';
    }
  }

  get traveler => null;

  // ✅ CRITICAL FIX: Safely parse required string fields from Supabase
  static String _parseRequiredString(dynamic value, String fieldName) {
    if (value == null) {
      throw ArgumentError('Required field $fieldName cannot be null');
    }

    if (value is String) {
      if (value.isEmpty) {
        throw ArgumentError('Required field $fieldName cannot be empty');
      }
      return value;
    }

    // Convert other types to string
    final stringValue = value.toString();
    if (stringValue.isEmpty || stringValue == 'null') {
      throw ArgumentError('Required field $fieldName has invalid value: $value');
    }

    return stringValue;
  }

  // ✅ CRITICAL FIX: Safely parse nullable string fields from Supabase
  static String? _parseNullableString(dynamic value) {
    if (value == null) {
      return null;
    }

    if (value is String) {
      return value.isEmpty ? null : value;
    }

    // Convert other types to string if needed
    return value.toString();
  }

  // ✅ CRITICAL FIX: Safely parse required DateTime fields from Supabase
  static DateTime _parseRequiredDateTime(dynamic value, String fieldName) {
    if (value == null) {
      throw ArgumentError('Required DateTime field $fieldName cannot be null');
    }

    if (value is String) {
      try {
        return DateTime.parse(value);
      } catch (e) {
        throw ArgumentError('Invalid DateTime format for $fieldName: $value');
      }
    }

    throw ArgumentError('Expected String for DateTime field $fieldName, got ${value.runtimeType}');
  }

  // ✅ CRITICAL FIX: Safely parse nullable DateTime fields from Supabase
  static DateTime? _parseNullableDateTime(dynamic value) {
    if (value == null) {
      return null;
    }

    if (value is String) {
      try {
        return DateTime.parse(value);
      } catch (e) {
        if (kDebugMode) {
          print('❌ Error parsing DateTime: $e');
        }
        return null;
      }
    }

    if (kDebugMode) {
      print('❌ Expected String for DateTime, got ${value.runtimeType}');
    }
    return null;
  }

  // ✅ CRITICAL FIX: Handle passenger_details parsing from Supabase
  static Map<String, dynamic>? _parsePassengerDetails(dynamic passengerDetailsData) {
    if (passengerDetailsData == null) {
      if (kDebugMode) {
        print('🔍 passenger_details is null');
      }
      return null;
    }

    if (kDebugMode) {
      print('🔍 Parsing passenger_details: ${passengerDetailsData.runtimeType}');
      print('🔍 Raw value: $passengerDetailsData');
    }

    // If it's already a Map, return it
    if (passengerDetailsData is Map<String, dynamic>) {
      if (kDebugMode) {
        print('✅ passenger_details is already Map<String, dynamic>');
      }
      return passengerDetailsData;
    }

    // If it's a Map<String, Object?> (common Supabase response), convert it
    if (passengerDetailsData is Map) {
      try {
        final converted = Map<String, dynamic>.from(passengerDetailsData);
        if (kDebugMode) {
          print('✅ Converted Map to Map<String, dynamic>: $converted');
        }
        return converted;
      } catch (e) {
        if (kDebugMode) {
          print('❌ Error converting Map to Map<String, dynamic>: $e');
        }
        return null;
      }
    }

    // ✅ CRITICAL: If it's a String (JSON from Supabase), parse it
    if (passengerDetailsData is String) {
      if (passengerDetailsData.trim().isEmpty) {
        if (kDebugMode) {
          print('🔍 passenger_details is empty string');
        }
        return null;
      }

      try {
        if (kDebugMode) {
          print('🔧 Decoding JSON string: $passengerDetailsData');
        }
        final decoded = jsonDecode(passengerDetailsData);

        if (decoded is Map<String, dynamic>) {
          if (kDebugMode) {
            print('✅ Successfully decoded JSON to Map<String, dynamic>: $decoded');
          }
          return decoded;
        } else if (decoded is Map) {
          final converted = Map<String, dynamic>.from(decoded);
          if (kDebugMode) {
            print('✅ Successfully decoded and converted JSON to Map<String, dynamic>: $converted');
          }
          return converted;
        } else {
          if (kDebugMode) {
            print('❌ Decoded JSON is not a Map: ${decoded.runtimeType}');
          }
          return null;
        }
      } catch (e) {
        if (kDebugMode) {
          print('❌ Error parsing passenger_details JSON: $e');
          print('❌ Raw data: $passengerDetailsData');
        }
        return null;
      }
    }

    // If it's any other type, log and return null
    if (kDebugMode) {
      print('❌ Unexpected passenger_details type: ${passengerDetailsData.runtimeType}');
      print('❌ Raw value: $passengerDetailsData');
    }
    return null;
  }
}
