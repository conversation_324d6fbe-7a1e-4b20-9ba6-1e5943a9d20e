import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../constants/app_theme.dart';
import '../../models/booking_model.dart';
import '../../providers/auth_provider.dart';
import '../../services/booking_service.dart';
import '../booking/booking_request_card.dart';
import '../chat/chat_page.dart';

class DriverRequestsPage extends StatefulWidget {
  const DriverRequestsPage({super.key});

  @override
  State<DriverRequestsPage> createState() => _DriverRequestsPageState();
}

class _DriverRequestsPageState extends State<DriverRequestsPage>
    with TickerProviderStateMixin {
  late TabController _tabController;
  
  List<BookingModel> _pendingRequests = [];
  List<BookingModel> _acceptedBookings = [];
  List<BookingModel> _completedBookings = [];
  
  bool _isLoading = true;
  RealtimeChannel? _bookingChannel;
  int _newRequestsCount = 0;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadBookings();
    _setupRealtimeSubscriptions();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _cleanupSubscriptions();
    super.dispose();
  }

  Future<void> _loadBookings() async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final currentUser = authProvider.currentUser;
    
    if (currentUser == null) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // Load bookings by status
      final pendingFuture = BookingService.getDriverBookingsByStatus(
        driverId: currentUser.id,
        status: 'pending',
      );
      
      final acceptedFuture = BookingService.getDriverBookingsByStatus(
        driverId: currentUser.id,
        status: 'accepted',
      );
      
      final completedFuture = BookingService.getDriverBookingsByStatus(
        driverId: currentUser.id,
        status: 'completed',
      );

      final results = await Future.wait([
        pendingFuture,
        acceptedFuture,
        completedFuture,
      ]);
      
      if (mounted) {
        setState(() {
          _pendingRequests = results[0];
          _acceptedBookings = results[1];
          _completedBookings = results[2];
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء تحميل الطلبات: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  void _setupRealtimeSubscriptions() {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final currentUser = authProvider.currentUser;
    
    if (currentUser == null) return;

    // Use the enhanced booking service subscription for pending requests
    _bookingChannel = BookingService.subscribeToPendingBookings(
      driverId: currentUser.id,
      onNewBookingRequest: (booking) {
        if (mounted) {
          setState(() {
            _pendingRequests.insert(0, booking);
            _newRequestsCount++;
          });
          
          // Show notification
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('طلب حجز جديد من ${booking.passenger?.fullName ?? 'مسافر'}'),
              backgroundColor: AppColors.info,
              action: SnackBarAction(
                label: 'عرض',
                textColor: Colors.white,
                onPressed: () {
                  _tabController.animateTo(0); // Go to pending tab
                },
              ),
            ),
          );
        }
      },
      onBookingStatusChange: (bookingId) {
        if (mounted) {
          _loadBookings(); // Refresh all data when status changes
        }
      },
      onError: (error) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في تحديث الطلبات: $error'),
              backgroundColor: AppColors.error,
            ),
          );
        }
      },
    );
  }

  void _cleanupSubscriptions() {
    if (_bookingChannel != null) {
      BookingService.unsubscribeFromBookings(_bookingChannel!);
    }
  }

  void _onBookingUpdated() {
    _loadBookings();
    setState(() {
      _newRequestsCount = 0; // Reset new requests count
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('طلبات الحجز'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            onPressed: _loadBookings,
            icon: const Icon(Icons.refresh),
          ),
        ],
      ),
      body: Column(
        children: [
          // Tab Bar
          Container(
            color: AppColors.primary,
            child: TabBar(
              controller: _tabController,
              indicatorColor: Colors.white,
              labelColor: Colors.white,
              unselectedLabelColor: Colors.white70,
              onTap: (index) {
                if (index == 0) {
                  setState(() {
                    _newRequestsCount = 0; // Reset when viewing pending tab
                  });
                }
              },
              tabs: [
                Tab(
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(Icons.pending, size: 18),
                      const SizedBox(width: 8),
                      const Text('طلبات جديدة'),
                      if (_pendingRequests.isNotEmpty || _newRequestsCount > 0) ...[
                        const SizedBox(width: 4),
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 6, vertical: 2),
                          decoration: BoxDecoration(
                            color: _newRequestsCount > 0 
                                ? AppColors.error 
                                : AppColors.warning,
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: Text(
                            '${_pendingRequests.length}',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
                Tab(
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(Icons.check_circle, size: 18),
                      const SizedBox(width: 8),
                      const Text('مقبولة'),
                      if (_acceptedBookings.isNotEmpty) ...[
                        const SizedBox(width: 4),
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 6, vertical: 2),
                          decoration: BoxDecoration(
                            color: AppColors.success,
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: Text(
                            '${_acceptedBookings.length}',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
                Tab(
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(Icons.done_all, size: 18),
                      const SizedBox(width: 8),
                      const Text('مكتملة'),
                      if (_completedBookings.isNotEmpty) ...[
                        const SizedBox(width: 4),
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 6, vertical: 2),
                          decoration: BoxDecoration(
                            color: AppColors.info,
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: Text(
                            '${_completedBookings.length}',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Tab Views
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : TabBarView(
                    controller: _tabController,
                    children: [
                      _RequestsList(
                        bookings: _pendingRequests,
                        emptyTitle: 'لا توجد طلبات جديدة',
                        emptySubtitle: 'ستظهر طلبات الحجز الجديدة هنا',
                        emptyIcon: Icons.pending_outlined,
                        onBookingUpdated: _onBookingUpdated,
                        showActions: true,
                      ),
                      _RequestsList(
                        bookings: _acceptedBookings,
                        emptyTitle: 'لا توجد حجوزات مقبولة',
                        emptySubtitle: 'ستظهر الحجوزات المقبولة هنا',
                        emptyIcon: Icons.check_circle_outline,
                        onBookingUpdated: _onBookingUpdated,
                        showActions: false,
                      ),
                      _RequestsList(
                        bookings: _completedBookings,
                        emptyTitle: 'لا توجد رحلات مكتملة',
                        emptySubtitle: 'ستظهر الرحلات المكتملة هنا',
                        emptyIcon: Icons.done_all_outlined,
                        onBookingUpdated: _onBookingUpdated,
                        showActions: false,
                      ),
                    ],
                  ),
          ),
        ],
      ),
    );
  }
}

class _RequestsList extends StatelessWidget {
  final List<BookingModel> bookings;
  final String emptyTitle;
  final String emptySubtitle;
  final IconData emptyIcon;
  final VoidCallback? onBookingUpdated;
  final bool showActions;

  const _RequestsList({
    required this.bookings,
    required this.emptyTitle,
    required this.emptySubtitle,
    required this.emptyIcon,
    this.onBookingUpdated,
    this.showActions = false,
  });

  @override
  Widget build(BuildContext context) {
    if (bookings.isEmpty) {
      return _buildEmptyState(context);
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: bookings.length,
      itemBuilder: (context, index) {
        final booking = bookings[index];

        if (showActions && booking.isPending) {
          // Show booking request card with accept/reject buttons
          return BookingRequestCard(
            booking: booking,
            onBookingUpdated: onBookingUpdated,
          );
        } else {
          // Show regular booking card with chat option
          return _AcceptedBookingCard(
            booking: booking,
            onBookingUpdated: onBookingUpdated,
          );
        }
      },
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            emptyIcon,
            size: 64,
            color: AppColors.textTertiary,
          ),
          const SizedBox(height: 16),
          Text(
            emptyTitle,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  color: AppColors.textSecondary,
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: 8),
          Text(
            emptySubtitle,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppColors.textTertiary,
                ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}

class _AcceptedBookingCard extends StatelessWidget {
  final BookingModel booking;
  final VoidCallback? onBookingUpdated;

  const _AcceptedBookingCard({
    required this.booking,
    this.onBookingUpdated,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with passenger info and status
            Row(
              children: [
                _buildPassengerAvatar(),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildPassengerInfo(),
                ),
                _buildStatusBadge(),
              ],
            ),

            const SizedBox(height: 16),

            // Trip details
            _buildTripDetails(),

            const SizedBox(height: 16),

            // Action buttons for accepted bookings
            if (booking.isAccepted) _buildActionButtons(context),
          ],
        ),
      ),
    );
  }

  Widget _buildPassengerAvatar() {
    return Container(
      width: 50,
      height: 50,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: Border.all(
          color: AppColors.primary.withValues(alpha: 0.3),
          width: 2,
        ),
      ),
      child: ClipOval(
        child: booking.passenger?.profileImageUrl != null
            ? Image.network(
                booking.passenger!.profileImageUrl!,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) => Container(
                  color: AppColors.surface,
                  child: const Icon(
                    Icons.person,
                    color: AppColors.textSecondary,
                    size: 24,
                  ),
                ),
              )
            : Container(
                color: AppColors.surface,
                child: const Icon(
                  Icons.person,
                  color: AppColors.textSecondary,
                  size: 24,
                ),
              ),
      ),
    );
  }

  Widget _buildPassengerInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          booking.passenger?.fullName ?? 'مسافر',
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        const SizedBox(height: 4),
        Row(
          children: [
            const Icon(
              Icons.star,
              color: AppColors.warning,
              size: 16,
            ),
            const SizedBox(width: 4),
            Text(
              booking.passenger?.rating?.toStringAsFixed(1) ?? '0.0',
              style: const TextStyle(
                color: AppColors.textSecondary,
                fontSize: 14,
              ),
            ),
            const SizedBox(width: 8),
            Text(
              booking.timeAgo,
              style: const TextStyle(
                color: AppColors.textTertiary,
                fontSize: 12,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStatusBadge() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: booking.statusColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: booking.statusColor.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            booking.statusIcon,
            size: 16,
            color: booking.statusColor,
          ),
          const SizedBox(width: 6),
          Text(
            booking.statusText,
            style: TextStyle(
              color: booking.statusColor,
              fontWeight: FontWeight.bold,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTripDetails() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.border),
      ),
      child: Row(
        children: [
          Expanded(
            child: Row(
              children: [
                const Icon(
                  Icons.airline_seat_recline_normal,
                  color: AppColors.primary,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  booking.seatsText,
                  style: const TextStyle(
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
          Text(
            booking.formattedTotalPrice,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              color: AppColors.secondary,
              fontSize: 16,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton.icon(
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => ChatPage(
                    tripId: booking.tripId,
                    otherUserId: booking.passengerId,
                    conversationId: null,
                  ),
                ),
              );
            },
            icon: const Icon(Icons.chat, size: 18),
            label: const Text('محادثة'),
            style: OutlinedButton.styleFrom(
              foregroundColor: AppColors.primary,
              side: const BorderSide(color: AppColors.primary),
              padding: const EdgeInsets.symmetric(vertical: 8),
            ),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: ElevatedButton.icon(
            onPressed: () => _completeBooking(context),
            icon: const Icon(Icons.done, size: 18),
            label: const Text('إكمال'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.success,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 8),
            ),
          ),
        ),
      ],
    );
  }

  Future<void> _completeBooking(BuildContext context) async {
    try {
      final result = await BookingService.completeBooking(booking.id);

      if (context.mounted) {
        if (result['success']) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(result['message']),
              backgroundColor: AppColors.success,
            ),
          );
          onBookingUpdated?.call();
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(result['error']),
              backgroundColor: AppColors.error,
            ),
          );
        }
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }
}
