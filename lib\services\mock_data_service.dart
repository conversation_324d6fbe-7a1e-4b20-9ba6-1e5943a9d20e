import '../models/user_model.dart';
import '../models/trip_model.dart';
import '../models/booking_model.dart';
import '../models/message_model.dart';
import '../models/rating_model.dart';
import '../constants/app_constants.dart';

class MockDataService {
  // Mock Users
  static List<UserModel> get mockUsers => [
        UserModel(
          id: 'user1',
          email: '<EMAIL>',
          phone: '+212612345678',
          fullName: 'أحمد محمد',
          role: AppConstants.roleTripLeader,
          profileImageUrl: null,
          bio: 'سائق محترف مع خبرة 10 سنوات في النقل بين المدن',
          city: 'الرباط',
          dateOfBirth: DateTime(1985, 5, 15),
          gender: 'male',
          isVerified: true,
          isLeader: true,
          balance: 150.0,
          rating: 4.8,
          totalTrips: 45,
          totalRatings: 42,
          badges: ['top_organizer', 'verified_driver', 'punctual'],
          createdAt: DateTime.now().subtract(const Duration(days: 365)),
          updatedAt: DateTime.now().subtract(const Duration(days: 1)),
        ),
        UserModel(
          id: 'user2',
          email: '<EMAIL>',
          phone: '+212687654321',
          fullName: 'فاطمة الزهراء',
          role: AppConstants.roleTraveler,
          profileImageUrl: null,
          bio: 'أحب السفر واستكشاف المدن المغربية الجميلة',
          city: 'الدار البيضاء',
          dateOfBirth: DateTime(1992, 8, 22),
          gender: 'female',
          isVerified: true,
          rating: 4.9,
          totalTrips: 23,
          totalRatings: 20,
          badges: ['frequent_traveler', 'friendly'],
          createdAt: DateTime.now().subtract(const Duration(days: 180)),
          updatedAt: DateTime.now().subtract(const Duration(hours: 2)),
        ),
        UserModel(
          id: 'user3',
          email: '<EMAIL>',
          phone: '+212712345678',
          fullName: 'يوسف بن علي',
          role: AppConstants.roleTripLeader,
          profileImageUrl: null,
          bio: 'متخصص في الرحلات العائلية والسياحية',
          city: 'فاس',
          dateOfBirth: DateTime(1988, 12, 3),
          gender: 'male',
          isVerified: true,
          isLeader: true,
          balance: 85.0,
          rating: 4.5,
          totalTrips: 18,
          totalRatings: 15,
          badges: ['family_friendly', 'helpful'],
          createdAt: DateTime.now().subtract(const Duration(days: 90)),
          updatedAt: DateTime.now().subtract(const Duration(days: 3)),
        ),
      ];

  // Mock Trips
  static List<TripModel> get mockTrips => [
        TripModel(
          id: 'trip1',
          leaderId: 'user1',
          leader: mockUsers[0],
          title: 'رحلة مريحة وآمنة',
          description: 'رحلة مباشرة من الرباط إلى مراكش مع توقف واحد للراحة',
          fromCity: 'الرباط',
          toCity: 'مراكش',
          departureDate: DateTime.now().add(const Duration(days: 3)),
          departureTime: '08:00',
          price: 120.0,
          totalSeats: 4,
          availableSeats: 2,
          tripType: AppConstants.tripTypeMixed,
          status: AppConstants.tripStatusActive,
          imageUrls: [],
          carModel: 'تويوتا كامري',
          carColor: 'أبيض',
          carPlateNumber: '12345 أ 67',
          rules: [
            'ممنوع التدخين',
            'الالتزام بالمواعيد',
            'احترام الركاب الآخرين'
          ],
          stops: [
            TripStop(
              name: 'محطة وقود شل',
              description: 'توقف للراحة والوقود',
              estimatedTime: DateTime.now()
                  .add(const Duration(days: 3, hours: 9, minutes: 30)),
              durationMinutes: 15,
            ),
          ],
          meetingPoint: {
            'name': 'محطة القطار الرباط المدينة',
            'address': 'شارع محمد الخامس، الرباط',
            'latitude': 34.0181,
            'longitude': -6.8186,
          },
          notes: 'يرجى الوصول قبل 15 دقيقة من موعد المغادرة',
          allowInstantBooking: true,
          rating: 4.7,
          totalRatings: 8,
          amenities: ['تكييف', 'موسيقى', 'شاحن هاتف'],
          createdAt: DateTime.now().subtract(const Duration(days: 5)),
          updatedAt: DateTime.now().subtract(const Duration(hours: 6)),
        ),
        TripModel(
          id: 'trip2',
          leaderId: 'user3',
          leader: mockUsers[2],
          title: 'رحلة عائلية مميزة',
          description: 'رحلة مناسبة للعائلات من فاس إلى شفشاون',
          fromCity: 'فاس',
          toCity: 'شفشاون',
          departureDate: DateTime.now().add(const Duration(days: 7)),
          departureTime: '10:00',
          price: 80.0,
          totalSeats: 6,
          availableSeats: 4,
          tripType: AppConstants.tripTypeFamilyOnly,
          status: AppConstants.tripStatusActive,
          imageUrls: [],
          carModel: 'هيونداي H1',
          carColor: 'أزرق',
          carPlateNumber: '98765 ب 43',
          rules: ['مناسبة للعائلات فقط', 'ممنوع التدخين', 'يُسمح بالأطفال'],
          stops: [],
          meetingPoint: {
            'name': 'ساحة بوجلود',
            'address': 'المدينة القديمة، فاس',
            'latitude': 34.0669,
            'longitude': -4.9684,
          },
          notes: 'رحلة مناسبة للعائلات مع أطفال',
          allowInstantBooking: false,
          bookingDeadline: DateTime.now().add(const Duration(days: 6)),
          rating: 4.9,
          totalRatings: 12,
          amenities: ['تكييف', 'مقاعد أطفال', 'وجبات خفيفة'],
          createdAt: DateTime.now().subtract(const Duration(days: 2)),
          updatedAt: DateTime.now().subtract(const Duration(hours: 1)),
        ),
        TripModel(
          id: 'trip3',
          leaderId: 'user1',
          leader: mockUsers[0],
          title: 'رحلة سريعة ومباشرة',
          description: 'رحلة مباشرة من الدار البيضاء إلى أكادير',
          fromCity: 'الدار البيضاء',
          toCity: 'أكادير',
          departureDate: DateTime.now().add(const Duration(days: 1)),
          departureTime: '14:00',
          price: 150.0,
          totalSeats: 3,
          availableSeats: 1,
          tripType: AppConstants.tripTypeMixed,
          status: AppConstants.tripStatusActive,
          imageUrls: [],
          carModel: 'مرسيدس C-Class',
          carColor: 'أسود',
          carPlateNumber: '11111 ج 22',
          rules: ['ممنوع التدخين', 'الالتزام بالمواعيد', 'حقيبة واحدة فقط'],
          stops: [
            TripStop(
              name: 'مطعم الطريق السيار',
              description: 'توقف لتناول الغداء',
              estimatedTime:
                  DateTime.now().add(const Duration(days: 1, hours: 16)),
              durationMinutes: 30,
            ),
          ],
          meetingPoint: {
            'name': 'مطار محمد الخامس',
            'address': 'نواصر، الدار البيضاء',
            'latitude': 33.3675,
            'longitude': -7.5898,
          },
          notes: 'رحلة سريعة بدون توقفات كثيرة',
          allowInstantBooking: true,
          rating: 4.6,
          totalRatings: 5,
          amenities: ['تكييف', 'واي فاي', 'مياه'],
          createdAt: DateTime.now().subtract(const Duration(hours: 12)),
          updatedAt: DateTime.now().subtract(const Duration(minutes: 30)),
        ),
      ];

  // Mock Bookings
  static List<BookingModel> get mockBookings => [
        BookingModel(
          id: 'booking1',
          tripId: 'trip1',
          passengerId: 'user2',
          driverId: 'user1',
          trip: mockTrips[0],
          passenger: mockUsers[1],
          seatsBooked: 1,
          totalPrice: 120.0,
          status: AppConstants.bookingStatusConfirmed,
          message: 'أتطلع للرحلة معكم',
          confirmedAt: DateTime.now().subtract(const Duration(hours: 2)),
          passengerDetails: {
            'passengers': [
              {
                'name': 'فاطمة الزهراء',
                'phone': '0612345678',
                'age': 31,
              }
            ]
          },
          specialRequests: 'مقعد بجانب النافذة إذا أمكن',
          isPaid: true,
          paymentMethod: 'cash',
          createdAt: DateTime.now().subtract(const Duration(days: 1)),
          updatedAt: DateTime.now().subtract(const Duration(hours: 2)),
        ),
        BookingModel(
          id: 'booking2',
          tripId: 'trip2',
          passengerId: 'user2',
          driverId: 'user1',
          trip: mockTrips[1],
          passenger: mockUsers[1],
          seatsBooked: 2,
          totalPrice: 160.0,
          status: AppConstants.bookingStatusPending,
          message: 'رحلة عائلية مع زوجي',
          passengerDetails: {
            'passengers': [
              {
                'name': 'فاطمة الزهراء',
                'phone': '0612345678',
                'age': 31,
              },
              {
                'name': 'محمد أحمد',
                'phone': '0687654321',
                'age': 35,
              }
            ]
          },
          specialRequests: 'نحتاج مقاعد متجاورة',
          isPaid: false,
          createdAt: DateTime.now().subtract(const Duration(hours: 6)),
          updatedAt: DateTime.now().subtract(const Duration(hours: 6)),
        ),
      ];

  // Mock Messages
  static List<MessageModel> get mockMessages => [
        MessageModel(
          id: 'msg1',
          tripId: 'trip1',
          senderId: 'user2',
          receiverId: 'user1',
          sender: mockUsers[1],
          receiver: mockUsers[0],
          content: 'السلام عليكم، هل الرحلة متاحة غداً؟',
          messageType: 'text',
          isRead: true,
          isDelivered: true,
          createdAt: DateTime.now().subtract(const Duration(hours: 1)),
          updatedAt: DateTime.now().subtract(const Duration(minutes: 30)),
        ),
        MessageModel(
          id: 'msg2',
          tripId: 'trip1',
          senderId: 'user1',
          receiverId: 'user2',
          sender: mockUsers[0],
          receiver: mockUsers[1],
          content: 'وعليكم السلام، نعم متاحة. يمكنك الحجز الآن',
          messageType: 'text',
          isRead: false,
          isDelivered: true,
          createdAt: DateTime.now().subtract(const Duration(minutes: 20)),
          updatedAt: DateTime.now().subtract(const Duration(minutes: 20)),
        ),
      ];

  // Mock Ratings
  static List<RatingModel> get mockRatings => [
        RatingModel(
          id: 'rating1',
          tripId: 'trip1',
          raterId: 'user2',
          ratedUserId: 'user1',
          trip: mockTrips[0],
          rater: mockUsers[1],
          ratedUser: mockUsers[0],
          rating: 5.0,
          review: 'رحلة ممتازة، سائق محترف ومهذب. أنصح بالتعامل معه',
          tags: ['punctual', 'friendly', 'safe_driver'],
          isAnonymous: false,
          createdAt: DateTime.now().subtract(const Duration(days: 2)),
          updatedAt: DateTime.now().subtract(const Duration(days: 2)),
        ),
      ];

  // Helper methods to get data by ID
  static UserModel? getUserById(String id) {
    try {
      return mockUsers.firstWhere((user) => user.id == id);
    } catch (e) {
      return null;
    }
  }

  static TripModel? getTripById(String id) {
    try {
      return mockTrips.firstWhere((trip) => trip.id == id);
    } catch (e) {
      return null;
    }
  }

  static List<TripModel> getTripsByLeader(String leaderId) {
    return mockTrips.where((trip) => trip.leaderId == leaderId).toList();
  }

  static List<BookingModel> getBookingsByTraveler(String passengerId) {
    return mockBookings
        .where((booking) => booking.passengerId == passengerId)
        .toList();
  }

  static List<BookingModel> getBookingsByTrip(String tripId) {
    return mockBookings.where((booking) => booking.tripId == tripId).toList();
  }
}
